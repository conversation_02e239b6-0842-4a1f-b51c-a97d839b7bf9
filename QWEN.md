# Mindflow Project - Lang<PERSON>hain RAG Application

## Project Overview

This directory contains a complete Retrieval Augmented Generation (RAG) application built with <PERSON><PERSON>hai<PERSON>. The application integrates with OpenRouter for LLM access and features a chat interface with session persistence using PostgreSQL. It's containerized with <PERSON>er for easy deployment.

## Key Components

### Application Code (`app/`)

The main application is located in the `app/` directory and includes:

1. **Backend**:
   - `main.py`: FastAPI application with REST endpoints
   - `rag_chain.py`: Implementation of the RAG pipeline
   - `chat_history.py`: PostgreSQL integration for storing chat history
   - `requirements.txt`: Python dependencies

2. **Frontend**:
   - `frontend/index.html`: Simple web interface for the chat application

3. **Infrastructure**:
   - `Dockerfile`: Docker configuration for the application
   - `docker-compose.yml`: Multi-container setup (application + PostgreSQL)
   - `.env.example`: Template for environment configuration
   - `README.md`: Documentation for the application
   - `HOW_TO_RUN.md`: Detailed instructions for running the application

### Research Documents (`research/`)

The `research/` directory contains documentation on various aspects of the project:

- <PERSON><PERSON>hain fundamentals
- RAG implementation patterns
- Chat models and vector stores
- Session persistence strategies
- Docker and Docker Compose usage
- OpenRouter integration
- Testing and extension strategies

## Technologies Used

- **Backend**: Python, FastAPI, LangChain
- **Frontend**: HTML, CSS, JavaScript
- **Database**: PostgreSQL
- **Vector Store**: FAISS
- **LLM Provider**: OpenRouter
- **Containerization**: Docker, Docker Compose

## How to Run the Application

1. Navigate to the `app/` directory
2. Copy `.env.example` to `.env` and add your OpenRouter API key
3. Run `docker-compose up --build`
4. Access the application at `http://localhost:8000`

For detailed instructions, see `app/HOW_TO_RUN.md`.

## Project Structure

```
Mindflow/
├── app/
│   ├── frontend/
│   │   └── index.html
│   ├── main.py
│   ├── rag_chain.py
│   ├── chat_history.py
│   ├── Dockerfile
│   ├── docker-compose.yml
│   ├── requirements.txt
│   ├── .env.example
│   ├── README.md
│   └── HOW_TO_RUN.md
├── research/
│   ├── langchain_overview.md
│   ├── langchain_rag.md
│   ├── langchain_chat_models.md
│   ├── langchain_vector_stores.md
│   ├── langchain_session_persistence.md
│   ├── docker_compose_langchain.md
│   ├── openrouter_integration.md
│   ├── project_summary.md
│   ├── extending_the_application.md
│   └── testing_the_application.md
├── PROJECT_SUMMARY.md
└── QWEN.md (this file)
```

## Key Features

1. **RAG Pipeline**:
   - Document loading from web sources
   - Text chunking and embedding
   - Vector storage with FAISS
   - Contextual retrieval

2. **LLM Integration**:
   - OpenRouter API integration
   - z-ai/glm-4.5-air:free model
   - Chat history-aware prompting

3. **Session Persistence**:
   - PostgreSQL database for chat history
   - Session-based conversation management
   - History retrieval and clearing

4. **Web Interface**:
   - Real-time chat interface
   - Session management
   - Responsive design

5. **Containerization**:
   - Docker container for application
   - Docker Compose for multi-service deployment
   - Environment-based configuration

## Customization

The application can be customized in several ways:

- **Documents**: Modify `rag_chain.py` to load documents from different sources
- **LLM Model**: Change the model in `rag_chain.py` to use a different OpenRouter model
- **Prompt**: Edit the template in `rag_chain.py` to modify how the LLM is prompted
- **Database**: Modify connection parameters in `chat_history.py` and `docker-compose.yml`

For more information on extending the application, see `research/extending_the_application.md`.

## Testing

The project includes documentation for various testing approaches:

- Manual testing procedures
- API testing with curl/Postman
- Unit testing with pytest
- Integration testing
- Load testing strategies

For detailed testing information, see `research/testing_the_application.md`.