# Lang<PERSON>hain Chat Models

## Overview

Chat models are a newer form of language models that accept messages as input and produce messages as output. They are designed for conversational applications and have largely replaced the older string-in, string-out model interface.

## Key Concepts

### Message Types

1. **SystemMessage**: Instructions for the model about how to behave
2. **HumanMessage**: Messages from the user
3. **AIMessage**: Messages from the AI assistant

### Core Methods

1. **invoke()**: Process a single list of messages
2. **stream()**: Stream tokens as they are generated
3. **batch()**: Process multiple lists of messages
4. **bind_tools()**: Bind tools to the model for function calling


## Integration with OpenRouter

OpenRouter provides access to hundreds of AI models through a single API endpoint. It's compatible with the OpenAI SDK, making it easy to use with LangChain's ChatOpenAI wrapper.

To use OpenRouter with LangChain:
1. Set the base URL to "https://openrouter.ai/api/v1"
2. Use your OpenRouter API key
3. Specify the model name (e.g., "openai/gpt-3.5-turbo")

## Best Practices

1. **Use Chat Models**: Prefer chat models over completion models for new applications
2. **Proper Message Formatting**: Always use the appropriate message types
3. **System Prompts**: Use system messages to guide model behavior
4. **Context Management**: Be mindful of token limits and context length
5. **Error Handling**: Implement proper error handling for API calls