from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import List, Optional
import os
from dotenv import load_dotenv

from rag_chain import create_rag_chain
from chat_history import ChatHistoryManager

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(title="LangChain RAG API", version="1.0.0")

# Mount static files for frontend
app.mount("/static", StaticFiles(directory="frontend"), name="static")

# Global variables for components
rag_chain = None
chat_history_manager = None

# Initialize components on startup
@app.on_event("startup")
async def startup_event():
    global rag_chain, chat_history_manager
    rag_chain = create_rag_chain()
    chat_history_manager = ChatHistoryManager()

# Request/Response models
class ChatRequest(BaseModel):
    session_id: str
    message: str

class ChatResponse(BaseModel):
    session_id: str
    response: str
    sources: Optional[List[str]] = None

class ChatHistoryResponse(BaseModel):
    session_id: str
    history: List[dict]

# API endpoints
@app.get("/")
async def root():
    """Serve the frontend HTML file"""
    return FileResponse("frontend/index.html")

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """Handle chat messages with RAG functionality"""
    global rag_chain, chat_history_manager
    
    # Get chat history for this session
    chat_history = chat_history_manager.get_history(request.session_id)
    
    # Prepare input with history context
    input_data = {
        "question": request.message,
        "chat_history": chat_history
    }
    
    # Get response from RAG chain
    result = await rag_chain.ainvoke(input_data)
    
    # Save the new interaction to history
    chat_history_manager.add_to_history(
        request.session_id, 
        request.message, 
        result
    )
    
    return ChatResponse(
        session_id=request.session_id,
        response=result
    )

@app.get("/history/{session_id}", response_model=ChatHistoryResponse)
async def get_history(session_id: str):
    """Get chat history for a session"""
    global chat_history_manager
    history = chat_history_manager.get_history(session_id)
    return ChatHistoryResponse(session_id=session_id, history=[{"role": msg[0], "content": msg[1]} for msg in history])

@app.delete("/history/{session_id}")
async def clear_history(session_id: str):
    """Clear chat history for a session"""
    global chat_history_manager
    chat_history_manager.clear_history(session_id)
    return {"message": f"History cleared for session {session_id}"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)