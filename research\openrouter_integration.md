# OpenRouter Integration with LangChain

## Overview

OpenRouter is a service that provides access to hundreds of AI models through a single API endpoint. It's compatible with the OpenAI API, making it easy to integrate with LangChain applications.

## Benefits

1. **Model Variety**: Access to models from OpenAI, Anthropic, Meta, Mistral, and others
2. **Cost Optimization**: Automatic fallback to cheaper models when possible
3. **Unified API**: Single endpoint for all supported models
4. **Free Tier**: Free access to certain models for development and testing

## Supported Models

OpenRouter provides access to a wide range of models including:
- OpenAI GPT models
- Anthropic Claude models
- Meta Llama models
- Mistral models
- Google Gemini models
- And many more

## Integration with LangChain

OpenRouter integrates seamlessly with LangChain through the ChatOpenAI class, since OpenRouter is compatible with the OpenAI API.

### Setup

1. Get an API key from OpenRouter
2. Install required packages:
   ```bash
   pip install langchain langchain-openai
   ```

3. Configure the ChatOpenAI model:
   ```python
   from langchain_openai import ChatOpenAI

   llm = ChatOpenAI(
       base_url="https://openrouter.ai/api/v1",
       api_key="YOUR_OPENROUTER_API_KEY",
       model="openai/gpt-3.5-turbo"  # or any other model available on OpenRouter
   )
   ```

### Using Free Models

OpenRouter offers several free models that are perfect for development and testing:
- `meta-llama/llama-3.1-8b-instruct:free`
- `microsoft/phi-3-mini-128k-instruct:free`
- `google/gemma-2-9b-it:free`
- `qwen/qwen-2-7b-instruct:free`

For our application, we'll use:
- `z-ai/glm-4.5-air:free` as specified in the requirements

### Configuration Options

You can pass additional configuration options to the ChatOpenAI model:

```python
llm = ChatOpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key="YOUR_OPENROUTER_API_KEY",
    model="z-ai/glm-4.5-air:free",
    temperature=0.7,
    max_tokens=1000,
    default_headers={
        "HTTP-Referer": "YOUR_SITE_URL",  # Optional
        "X-Title": "YOUR_APP_NAME",       # Optional
    }
)
```

## Best Practices

1. **API Key Security**: Store API keys in environment variables or .env files
2. **Model Selection**: Choose appropriate models for your use case (consider cost, performance, and capabilities)
3. **Rate Limiting**: Be aware of rate limits and implement appropriate error handling
4. **Fallback Models**: Consider specifying fallback models for improved reliability
5. **Monitoring**: Track usage and costs, especially when using paid models
6. **Testing**: Test with free models during development to minimize costs

## Error Handling

When using OpenRouter with LangChain, implement proper error handling for:
- API key validation errors
- Rate limiting
- Model availability issues
- Network connectivity problems

```python
try:
    response = llm.invoke("Hello, world!")
    print(response.content)
except Exception as e:
    print(f"Error calling OpenRouter: {e}")
```