import psycopg2
from psycopg2.extras import RealDictCursor
import os
from typing import List, <PERSON><PERSON>
from datetime import datetime

class ChatHistoryManager:
    """Manage chat history with PostgreSQL database"""
    
    def __init__(self):
        # Database connection parameters
        self.db_params = {
            'host': os.getenv('DB_HOST', 'db'),
            'database': os.getenv('POSTGRES_DB', 'langchain_rag'),
            'user': os.getenv('POSTGRES_USER', 'langchain'),
            'password': os.getenv('POSTGRES_PASSWORD', 'langchain123'),
            'port': os.getenv('DB_PORT', '5432')
        }
        
        # Create tables if they don't exist
        self._initialize_database()
    
    def _get_connection(self):
        """Get database connection"""
        return psycopg2.connect(**self.db_params)
    
    def _initialize_database(self):
        """Create chat_history table if it doesn't exist"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS chat_history (
                            id SERIAL PRIMARY KEY,
                            session_id VARCHAR(255) NOT NULL,
                            message_type VARCHAR(10) NOT NULL,
                            content TEXT NOT NULL,
                            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    conn.commit()
        except Exception as e:
            print(f"Error initializing database: {e}")
    
    def add_to_history(self, session_id: str, user_message: str, ai_response: str):
        """Add user message and AI response to chat history"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    # Add user message
                    cur.execute("""
                        INSERT INTO chat_history (session_id, message_type, content)
                        VALUES (%s, %s, %s)
                    """, (session_id, 'user', user_message))
                    
                    # Add AI response
                    cur.execute("""
                        INSERT INTO chat_history (session_id, message_type, content)
                        VALUES (%s, %s, %s)
                    """, (session_id, 'ai', ai_response))
                    
                    conn.commit()
        except Exception as e:
            print(f"Error adding to history: {e}")
    
    def get_history(self, session_id: str) -> List[Tuple[str, str]]:
        """Get chat history for a session as a list of (role, content) tuples"""
        try:
            with self._get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cur:
                    cur.execute("""
                        SELECT message_type, content, timestamp
                        FROM chat_history
                        WHERE session_id = %s
                        ORDER BY timestamp ASC
                    """, (session_id,))
                    
                    rows = cur.fetchall()
                    # Convert to the format expected by our application
                    history = []
                    for row in rows:
                        if row['message_type'] == 'user':
                            history.append(('user', row['content']))
                        elif row['message_type'] == 'ai':
                            history.append(('assistant', row['content']))
                    
                    return history
        except Exception as e:
            print(f"Error getting history: {e}")
            return []
    
    def clear_history(self, session_id: str):
        """Clear chat history for a session"""
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("""
                        DELETE FROM chat_history
                        WHERE session_id = %s
                    """, (session_id,))
                    conn.commit()
        except Exception as e:
            print(f"Error clearing history: {e}")