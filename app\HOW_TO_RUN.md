# How to Run the LangChain RAG Application

Follow these steps to run the LangChain RAG application:

## Prerequisites

1. **<PERSON><PERSON> and <PERSON>er Compose** must be installed on your system
2. **An OpenRouter API key** (free tier available at https://openrouter.ai/)

## Setup Instructions

### 1. Clone the Repository
```bash
git clone <repository-url>
cd <repository-directory>
```

### 2. Navigate to the App Directory
```bash
cd app
```

### 3. Configure Environment Variables
Copy the example environment file and edit it:
```bash
cp .env.example .env
```

Edit the `.env` file and replace `your_openrouter_api_key_here` with your actual OpenRouter API key:
```env
# OpenRouter API Key (required)
OPENROUTER_API_KEY=your_actual_openrouter_api_key_here

# Database connection
POSTGRES_USER=langchain
POSTGRES_PASSWORD=langchain123
POSTGRES_DB=langchain_rag
```

### 4. Build and Run with <PERSON><PERSON> Compose
```bash
docker-compose up --build
```

This command will:
- Build the application Docker image
- Start a PostgreSQL database container
- Start the FastAPI application container
- Link the containers together

### 5. Access the Application
Open your web browser and navigate to:
```
http://localhost:8000
```

## Using the Application

1. **Chat Interface**:
   - Enter a session ID (or use the default "session1")
   - Type your message in the input box
   - Press Enter or click "Send" to submit
   - View the AI response in the chat history

2. **Session Management**:
   - Use different session IDs to maintain separate conversations
   - Click "Clear History" to reset a conversation

3. **API Endpoints** (for direct access):
   - `POST /chat`: Send a message and get a response
   - `GET /history/{session_id}`: Retrieve chat history
   - `DELETE /history/{session_id}`: Clear chat history

## Stopping the Application

To stop the application, press `Ctrl+C` in the terminal where docker-compose is running.

To stop and remove containers:
```bash
docker-compose down
```

To stop and remove containers along with volumes (deletes database data):
```bash
docker-compose down -v
```

## Troubleshooting

### Common Issues

1. **Port Already in Use**:
   If you get an error about ports being in use, you can modify the `docker-compose.yml` file to use different ports:
   ```yaml
   ports:
     - "8001:8000"  # Change host port to 8001
   ```

2. **Database Connection Errors**:
   If the application can't connect to the database, try:
   ```bash
   docker-compose down -v
   docker-compose up --build
   ```

3. **API Key Issues**:
   - Verify your OpenRouter API key is correct
   - Check that you have access to the `z-ai/glm-4.5-air:free` model
   - Ensure the API key is properly set in the `.env` file

4. **Docker Permission Issues**:
   On Linux, you might need to run Docker commands with `sudo` or add your user to the docker group.

### Logs

To view application logs:
```bash
docker-compose logs app
```

To view database logs:
```bash
docker-compose logs db
```

To view all logs:
```bash
docker-compose logs
```

## Development

To make changes to the application:

1. Modify the Python files in the `app` directory
2. Rebuild and restart the containers:
   ```bash
   docker-compose down
   docker-compose up --build
   ```

3. Or for faster development, you can run the application locally:
   ```bash
   # Install dependencies
   pip install -r requirements.txt
   
   # Run the application
   python main.py
   ```

Note: When running locally, you'll need to have PostgreSQL running on your system and update the database connection parameters accordingly.

## Customization

### Changing the LLM Model
Edit `rag_chain.py` and modify the `model` parameter in the ChatOpenAI initialization.

### Adding More Documents
Modify the `create_rag_chain()` function in `rag_chain.py` to load documents from different sources.

### Changing the Prompt
Edit the template in `rag_chain.py` to modify how the LLM is prompted.

This should get you started with running and using the LangChain RAG application!