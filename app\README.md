# Lang<PERSON>hain RAG Application with OpenRouter

This is a simple Retrieval Augmented Generation (RAG) application built with LangChain that uses OpenRouter as the LLM provider and includes chat session persistence.

## Features

- RAG pipeline with document loading, embedding, and retrieval
- Chat interface with session persistence
- Docker containerization for easy deployment
- PostgreSQL database for storing chat history
- OpenRouter integration for LLM access
- Simple web frontend

## Prerequisites

- Docker and Docker Compose
- An OpenRouter API key (free tier available)

## Setup

1. Clone this repository

2. Navigate to the app directory:
   ```bash
   cd app
   ```

3. Copy the example environment file and update it with your OpenRouter API key:
   ```bash
   cp .env.example .env
   ```
   Then edit the `.env` file and replace `your_openrouter_api_key_here` with your actual OpenRouter API key.

4. Build and run with Docker Compose:
   ```bash
   docker-compose up --build
   ```

5. Access the application at `http://localhost:8000`

## Usage

1. Open your browser and go to `http://localhost:8000`
2. Enter a session ID (or use the default "session1")
3. Type your message in the input box and press Enter or click Send
4. The application will respond using the RAG pipeline with chat history context
5. You can switch between different session IDs to maintain separate conversations
6. Use the "Clear History" button to reset a conversation

## Project Structure

- `main.py`: FastAPI application with endpoints
- `rag_chain.py`: RAG chain implementation
- `chat_history.py`: Chat history management with PostgreSQL
- `Dockerfile`: Docker configuration for the application
- `docker-compose.yml`: Multi-container setup (app + database)
- `requirements.txt`: Python dependencies
- `frontend/`: Simple HTML/JavaScript frontend

## How It Works

1. On startup, the application loads a sample document about Artificial Intelligence from Wikipedia
2. The document is split into chunks and embedded using OpenAI embeddings
3. The embeddings are stored in a FAISS vector store
4. When you send a message, the application:
   - Retrieves relevant document chunks using similarity search
   - Formats the chat history
   - Sends the context, question, and chat history to the LLM via OpenRouter
   - Returns the response and saves it to the chat history

## Customization

- To use different documents, modify the `create_rag_chain()` function in `rag_chain.py`
- To change the LLM model, update the `model` parameter in the ChatOpenAI initialization
- To modify the prompt, edit the template in `rag_chain.py`
- To change the database, modify the connection parameters in `chat_history.py` and `docker-compose.yml`

## Troubleshooting

- If you get connection errors, make sure Docker is running
- If the application fails to start, check that your OpenRouter API key is valid
- If you get database errors, try running `docker-compose down -v` to remove volumes and start fresh