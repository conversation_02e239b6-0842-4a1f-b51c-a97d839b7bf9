# Testing the LangChain RAG Application

This document provides guidance on how to test the LangChain RAG application we've built, including unit tests, integration tests, and manual testing procedures.

## 1. Manual Testing

### Basic Functionality Testing

1. **Application Startup**
   - Run `docker-compose up --build`
   - Verify that both containers (app and db) start without errors
   - Check that the application is accessible at `http://localhost:8000`

2. **Frontend Testing**
   - Open the browser and navigate to `http://localhost:8000`
   - Verify that the chat interface loads correctly
   - Check that all UI elements are visible and functional:
     - Session ID input
     - Message input field
     - Send button
     - Clear History button

3. **Basic Chat Functionality**
   - Enter a message in the chat input (e.g., "What is artificial intelligence?")
   - Verify that:
     - The user message appears in the chat history
     - The AI response is received and displayed
     - The response is relevant to the question and based on the loaded document

4. **Session Persistence**
   - Send a few messages in one session
   - Refresh the page or close and reopen the browser
   - Verify that the chat history is preserved
   - Try a different session ID and verify it has separate history

5. **History Management**
   - Send several messages in a session
   - Click "Clear History"
   - Verify that the chat history is cleared
   - Send a new message and verify that the conversation starts fresh

### Edge Case Testing

1. **Empty Messages**
   - Try sending empty messages
   - Verify that the application handles this gracefully

2. **Very Long Messages**
   - Send extremely long messages
   - Verify that the application handles this without breaking

3. **Special Characters**
   - Send messages with special characters, emojis, etc.
   - Verify that these are handled correctly

4. **Network Issues**
   - Simulate network issues by temporarily disconnecting
   - Verify that the application provides appropriate error messages

## 2. API Testing

### Using curl

Test the API endpoints directly using curl:

1. **Chat Endpoint**
   ```bash
   curl -X POST "http://localhost:8000/chat" \
        -H "Content-Type: application/json" \
        -d '{"session_id": "test1", "message": "What is AI?"}'
   ```

2. **History Endpoint**
   ```bash
   curl "http://localhost:8000/history/test1"
   ```

3. **Clear History Endpoint**
   ```bash
   curl -X DELETE "http://localhost:8000/history/test1"
   ```

### Using Postman or Similar Tools

Create a collection with the following requests:
1. POST /chat
2. GET /history/{session_id}
3. DELETE /history/{session_id}

## 3. Unit Testing

Create a `tests` directory and add unit tests for individual components:

### Testing the RAG Chain

Create `tests/test_rag_chain.py`:

```python
import pytest
import os
from unittest.mock import patch, MagicMock
from rag_chain import create_rag_chain, format_docs, format_chat_history

# Mock environment variables
os.environ["OPENROUTER_API_KEY"] = "test-key"

def test_format_docs():
    """Test the format_docs function"""
    mock_docs = [
        MagicMock(page_content="Document 1 content"),
        MagicMock(page_content="Document 2 content")
    ]
    
    result = format_docs(mock_docs)
    expected = "Document 1 content

Document 2 content"
    
    assert result == expected

def test_format_chat_history():
    """Test the format_chat_history function"""
    chat_history = [
        ("user", "Hello"),
        ("assistant", "Hi there!")
    ]
    
    result = format_chat_history(chat_history)
    expected = "Human: Hello
Assistant: Hi there!"
    
    assert result == expected

@patch('rag_chain.WebBaseLoader')
@patch('rag_chain.FAISS')
def test_create_rag_chain(mock_faiss, mock_loader):
    """Test that the RAG chain can be created"""
    # Mock the loader and vector store
    mock_loader_instance = MagicMock()
    mock_loader.return_value = mock_loader_instance
    mock_loader_instance.load.return_value = []
    
    mock_faiss_instance = MagicMock()
    mock_faiss.from_documents.return_value = mock_faiss_instance
    mock_faiss_instance.as_retriever.return_value = MagicMock()
    
    # Create the chain
    chain = create_rag_chain()
    
    # Verify that the chain was created
    assert chain is not None
```

### Testing Chat History Manager

Create `tests/test_chat_history.py`:

```python
import pytest
from unittest.mock import patch, MagicMock
import os
from chat_history import ChatHistoryManager

# Mock environment variables
os.environ["POSTGRES_USER"] = "test"
os.environ["POSTGRES_PASSWORD"] = "test"
os.environ["POSTGRES_DB"] = "test"

@patch('chat_history.psycopg2.connect')
def test_chat_history_manager_initialization(mock_connect):
    """Test that the ChatHistoryManager initializes correctly"""
    mock_conn = MagicMock()
    mock_connect.return_value = mock_conn
    
    manager = ChatHistoryManager()
    
    # Verify that the connection was attempted
    mock_connect.assert_called_once()
    
    # Verify that the table creation was attempted
    mock_conn.cursor.assert_called_once()

@patch('chat_history.psycopg2.connect')
def test_add_to_history(mock_connect):
    """Test adding messages to history"""
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    mock_connect.return_value = mock_conn
    
    manager = ChatHistoryManager()
    manager.add_to_history("session1", "Hello", "Hi there!")
    
    # Verify that two insert statements were executed
    assert mock_cursor.execute.call_count == 2
    assert mock_conn.commit.call_count == 1

@patch('chat_history.psycopg2.connect')
def test_get_history(mock_connect):
    """Test retrieving chat history"""
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    
    # Mock the fetchall return value
    mock_cursor.fetchall.return_value = [
        {'message_type': 'user', 'content': 'Hello', 'timestamp': '2023-01-01 00:00:00'},
        {'message_type': 'ai', 'content': 'Hi there!', 'timestamp': '2023-01-01 00:00:01'}
    ]
    
    mock_connect.return_value = mock_conn
    
    manager = ChatHistoryManager()
    history = manager.get_history("session1")
    
    # Verify the returned history format
    assert len(history) == 2
    assert history[0] == ('user', 'Hello')
    assert history[1] == ('assistant', 'Hi there!')

@patch('chat_history.psycopg2.connect')
def test_clear_history(mock_connect):
    """Test clearing chat history"""
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    mock_connect.return_value = mock_conn
    
    manager = ChatHistoryManager()
    manager.clear_history("session1")
    
    # Verify that the delete statement was executed
    mock_cursor.execute.assert_called_once()
    mock_conn.commit.assert_called_once()
```

## 4. Integration Testing

Create `tests/test_integration.py`:

```python
import pytest
import asyncio
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)

def test_root_endpoint():
    """Test the root endpoint"""
    response = client.get("/")
    assert response.status_code == 200

def test_chat_endpoint():
    """Test the chat endpoint"""
    chat_data = {
        "session_id": "test_session",
        "message": "What is artificial intelligence?"
    }
    
    response = client.post("/chat", json=chat_data)
    assert response.status_code == 200
    
    data = response.json()
    assert "session_id" in data
    assert "response" in data
    assert data["session_id"] == "test_session"

def test_history_endpoints():
    """Test the history endpoints"""
    session_id = "test_history_session"
    
    # First, send a message
    chat_data = {
        "session_id": session_id,
        "message": "Test message"
    }
    
    response = client.post("/chat", json=chat_data)
    assert response.status_code == 200
    
    # Then, get the history
    response = client.get(f"/history/{session_id}")
    assert response.status_code == 200
    
    data = response.json()
    assert "session_id" in data
    assert "history" in data
    assert data["session_id"] == session_id
    
    # Finally, clear the history
    response = client.delete(f"/history/{session_id}")
    assert response.status_code == 200

def test_clear_empty_history():
    """Test clearing history for a non-existent session"""
    response = client.delete("/history/non_existent_session")
    assert response.status_code == 200
```

## 5. Running Tests

### Setting Up Test Environment

1. Install test dependencies:
   ```bash
   pip install pytest pytest-asyncio fastapi[all] httpx
   ```

2. Create a test configuration in `pytest.ini`:
   ```ini
   [tool:pytest]
   testpaths = tests
   python_files = test_*.py
   python_classes = Test*
   python_functions = test_*
   addopts = -v
   ```

### Running Tests

1. **Unit Tests**:
   ```bash
   pytest tests/test_rag_chain.py
   pytest tests/test_chat_history.py
   ```

2. **Integration Tests**:
   ```bash
   pytest tests/test_integration.py
   ```

3. **All Tests**:
   ```bash
   pytest
   ```

4. **With Coverage**:
   ```bash
   pip install pytest-cov
   pytest --cov=. --cov-report=html
   ```

## 6. Continuous Integration

Create a `.github/workflows/test.yml` file for GitHub Actions:

```yaml
name: Test Application

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        pip install -r app/requirements.txt
        pip install pytest pytest-asyncio fastapi[all] httpx pytest-cov
    
    - name: Run tests
      run: |
        cd app
        pytest --cov=. --cov-report=xml
      env:
        OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}
        POSTGRES_USER: test
        POSTGRES_PASSWORD: test
        POSTGRES_DB: test
        DB_HOST: localhost
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v1
```

## 7. Load Testing

For load testing, you can use tools like `locust`:

1. Install locust:
   ```bash
   pip install locust
   ```

2. Create `locustfile.py`:
   ```python
   from locust import HttpUser, task, between
   
   class RAGUser(HttpUser):
       wait_time = between(1, 5)
       
       @task
       def chat_with_ai(self):
           self.client.post("/chat", json={
               "session_id": "load_test",
               "message": "What is artificial intelligence?"
           })
   ```

3. Run load test:
   ```bash
   locust -f locustfile.py
   ```

This testing approach ensures that your LangChain RAG application is robust, reliable, and performs well under various conditions.