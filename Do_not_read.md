# Student Helper App - Incremental Development Plan with HIAD System

## Phase 0: Foundation & Planning (Days 1-2)

### Step 1: Create Project Specification
**AI Agent Role**: Product Analyst
```
Input: Initial idea document
Process:
1. AI analyzes requirements
2. Creates detailed PRD (Product Requirements Document)
3. Identifies tech stack: React, Lang<PERSON>hain, LangGraph, LangSearch
4. Lists all dependencies and APIs needed
Output: Complete PRD with technical specifications
```

### Step 2: Architecture Design
**AI Agent Role**: System Architect
```
Input: PRD from Step 1
Process:
1. Create system architecture diagram
2. Define data models
3. Design API structure
4. Plan component hierarchy
Output: Architecture document with diagrams
```

### Step 3: Research & Documentation Gathering
**AI Agent Role**: Research Assistant
```
Input: Architecture document
Process:
1. Gather latest LangChain documentation
2. Collect LangGraph implementation examples
3. Research OpenRouter free models API
4. Compile RAG system best practices
5. Study WebSocket implementations for real-time updates
Output: Research bundle (flattened documentation file)
```

## Phase 1: Minimal Core (Days 3-4)

### Iteration 1.1: Basic Chat Interface
**Context**: PRD + Architecture + Research Bundle
```yaml
Features:
  - Simple React app with single chat window
  - Local state management (no backend yet)
  - Basic message display (user/AI bubbles)
  
Tests:
  - Component renders
  - Messages display correctly
  - Input field works
  
Implementation:
  - src/App.jsx
  - src/components/ChatWindow.jsx
  - src/components/MessageBubble.jsx
```

### Iteration 1.2: OpenRouter Integration
**Context**: Previous code + OpenRouter API docs
```yaml
Features:
  - Connect to OpenRouter API
  - Use free model (e.g., meta-llama/llama-3.2-3b-instruct:free)
  - Stream responses
  
Tests:
  - API connection successful
  - Streaming works
  - Error handling present
  
Implementation:
  - src/services/openRouterService.js
  - Update ChatWindow with API calls
```

### Iteration 1.3: Session Management
**Context**: Current codebase + session requirements
```yaml
Features:
  - Create new chat sessions
  - Switch between sessions
  - Persist to localStorage
  
Tests:
  - Sessions create/delete
  - Data persists on refresh
  - Session switching works
  
Implementation:
  - src/stores/sessionStore.js
  - src/components/SessionList.jsx
```

## Phase 2: Agent Foundation (Days 5-7)

### Iteration 2.1: LangChain Setup
**Context**: Flattened codebase + LangChain docs
```yaml
Features:
  - Initialize LangChain with OpenRouter
  - Create base agent class
  - Implement simple tool calling
  
Tests:
  - LangChain initializes
  - Agent responds
  - Tool calls execute
  
Implementation:
  - src/agents/baseAgent.js
  - src/config/langchainConfig.js
```

### Iteration 2.2: Tool System Architecture
**Context**: Current code + tool design patterns
```yaml
Features:
  - Tool interface definition
  - Tool registry system
  - Tool execution framework
  
Tests:
  - Tools register correctly
  - Execution pipeline works
  - Results return properly
  
Implementation:
  - src/tools/toolRegistry.js
  - src/tools/baseTool.js
  - src/tools/toolExecutor.js
```

### Iteration 2.3: Visual Tool Feedback
**Context**: Tool system + UI requirements
```yaml
Features:
  - Tool usage indicators
  - Animated status messages
  - Progress visualization
  
Tests:
  - Animations trigger
  - Status updates display
  - Multiple tools show correctly
  
Implementation:
  - src/components/ToolIndicator.jsx
  - src/components/AgentStatus.jsx
  - src/hooks/useToolAnimation.js
```

## Phase 3: Core Features (Days 8-12)

### Iteration 3.1: To-Do List Feature
**Context**: Tool system + to-do requirements
```yaml
Features:
  - To-do data model
  - CRUD operations
  - Agent tool for to-do access
  
Tests:
  - Create/read/update/delete tasks
  - Agent can access todos
  - Persistence works
  
Implementation:
  - src/tools/todoTool.js
  - src/stores/todoStore.js
  - src/components/TodoList.jsx
```

### Iteration 3.2: Calendar Integration
**Context**: Current code + calendar requirements
```yaml
Features:
  - Calendar data model
  - Event CRUD operations
  - Agent calendar tool
  
Tests:
  - Events create/update
  - Calendar displays correctly
  - Agent schedules events
  
Implementation:
  - src/tools/calendarTool.js
  - src/stores/calendarStore.js
  - src/components/Calendar.jsx
```

### Iteration 3.3: Subject Management
**Context**: Education domain + subject organization
```yaml
Features:
  - Subject creation/deletion
  - Subject-specific todos
  - Subject-specific calendar
  
Tests:
  - Subjects CRUD works
  - Filtering by subject
  - Cross-references work
  
Implementation:
  - src/stores/subjectStore.js
  - src/components/SubjectManager.jsx
  - Update todo/calendar tools
```

## Phase 4: RAG System (Days 13-16)

### Iteration 4.1: Document Storage
**Context**: RAG requirements + vector DB research
```yaml
Features:
  - Document upload interface
  - File processing pipeline
  - Local vector storage setup
  
Tests:
  - Files upload successfully
  - Processing completes
  - Storage persists
  
Implementation:
  - src/services/documentProcessor.js
  - src/stores/vectorStore.js
  - src/components/DocumentUpload.jsx
```

### Iteration 4.2: Semantic Search
**Context**: Vector store + LangSearch docs
```yaml
Features:
  - Embedding generation
  - Similarity search
  - Result ranking
  
Tests:
  - Embeddings generate
  - Search returns results
  - Relevance scoring works
  
Implementation:
  - src/services/embeddingService.js
  - src/services/searchService.js
  - src/tools/ragTool.js
```

### Iteration 4.3: Study Notes Editor
**Context**: Current RAG + markdown requirements
```yaml
Features:
  - Markdown editor component
  - Auto-save functionality
  - Link to RAG system
  
Tests:
  - Editor saves content
  - Markdown renders
  - RAG indexes notes
  
Implementation:
  - src/components/MarkdownEditor.jsx
  - src/stores/notesStore.js
  - Integration with RAG
```

## Phase 5: Advanced Agent System (Days 17-20)

### Iteration 5.1: LangGraph Orchestration
**Context**: All code + LangGraph documentation
```yaml
Features:
  - Graph-based agent flow
  - State management
  - Conditional routing
  
Tests:
  - Graph executes correctly
  - State transitions work
  - Routing logic functions
  
Implementation:
  - src/agents/orchestrator.js
  - src/agents/graphDefinition.js
  - src/agents/stateManager.js
```

### Iteration 5.2: Sub-Agent System
**Context**: Orchestrator + sub-agent pattern
```yaml
Features:
  - Sub-agent spawning
  - Context isolation
  - Result aggregation
  
Tests:
  - Sub-agents spawn
  - Isolation works
  - Results return correctly
  
Implementation:
  - src/agents/subAgentManager.js
  - src/agents/subAgent.js
  - src/components/SubAgentVisualizer.jsx
```

### Iteration 5.3: Research Assistant
**Context**: Sub-agent system + web search requirements
```yaml
Features:
  - Web search capability
  - Academic source filtering
  - Summary generation
  
Tests:
  - Search executes
  - Filtering works
  - Summaries generate
  
Implementation:
  - src/tools/webSearchTool.js
  - src/agents/researchAgent.js
  - src/services/academicFilter.js
```

## Phase 6: Study Mode (Days 21-23)

### Iteration 6.1: Study Program Generator
**Context**: All features + educational AI patterns
```yaml
Features:
  - Analyze uploaded materials
  - Generate study plans
  - Adaptive scheduling
  
Tests:
  - Analysis completes
  - Plans generate
  - Schedule adapts
  
Implementation:
  - src/agents/studyPlanAgent.js
  - src/services/curriculumGenerator.js
  - src/components/StudyPlan.jsx
```

### Iteration 6.2: Teaching Mode
**Context**: Study plans + teaching strategies
```yaml
Features:
  - Interactive lessons
  - Question generation
  - Progress tracking
  
Tests:
  - Lessons generate
  - Questions relevant
  - Progress saves
  
Implementation:
  - src/agents/teacherAgent.js
  - src/services/lessonGenerator.js
  - src/components/TeachingInterface.jsx
```

## Phase 7: Polish & Optimization (Days 24-25)

### Iteration 7.1: Performance Optimization
```yaml
Features:
  - Code splitting
  - Lazy loading
  - Caching strategies
  
Tests:
  - Load times improved
  - Memory usage optimal
  - Cache works correctly
```

### Iteration 7.2: UI/UX Polish
```yaml
Features:
  - Smooth animations
  - Responsive design
  - Accessibility features
  
Tests:
  - Animations smooth
  - Mobile responsive
  - WCAG compliance
```

## Implementation Strategy for Each Iteration

### 1. Context Preparation
```bash
# Before each iteration:
npx bmad-method flatten --input ./src --output ./context/current-code.xml

# Combine with requirements:
cat context/current-code.xml docs/requirements.md docs/architecture.md > context/full-context.xml
```

### 2. Test-First Development
```javascript
// For each feature, write tests first:
describe('Feature: Todo Tool', () => {
  test('should create a new todo', async () => {
    const result = await todoTool.create({
      title: 'Study for exam',
      subject: 'Biology'
    });
    expect(result.success).toBe(true);
  });
});
```

### 3. Implementation with AI
```markdown
Prompt Template:
"Given this context [attach full-context.xml], implement the following feature:
- Feature: [specific feature]
- Tests to pass: [attach test file]
- Use these patterns: [attach relevant docs]
- Ensure: No placeholders, full implementation, all tests pass"
```

### 4. Validation Loop
```bash
# After implementation:
npm test -- --coverage
npm run lint
npm run type-check

# If tests fail, provide error to AI:
"The following tests failed: [errors]. Fix the implementation."
```

## Critical Success Factors

### 1. Context Management Rules
- **Never lose context**: Always include full codebase in prompts
- **Document everything**: Each iteration adds to documentation
- **Version control**: Commit after each successful iteration

### 2. Testing Protocol
- **100% test coverage** for each iteration before moving on
- **Integration tests** between features
- **End-to-end tests** using Playwright MCP

### 3. AI Coder Instructions Template
```markdown
You are implementing iteration [X.Y] of a student helper application.

Context:
- Current codebase: [attached]
- Requirements: [specific for this iteration]
- Dependencies available: React, LangChain, LangGraph, LangSearch
- API: OpenRouter with free models

Rules:
1. NO placeholder code - everything must be functional
2. NO console.log debugging - use proper error handling
3. All tests must pass
4. Use existing patterns from codebase
5. Document all new functions

Implement: [specific feature]
Ensure: [specific tests pass]
```

### 4. Debugging Strategy
When the AI makes errors:
1. **Identify the specific error** (not working ≠ helpful)
2. **Provide exact error messages**
3. **Include relevant code context**
4. **Ask for specific fix** (not "fix everything")

## Visual Progress Tracking

```mermaid
gantt
    title Development Timeline
    dateFormat  YYYY-MM-DD
    section Phase 0
    Planning & Research     :2024-01-01, 2d
    section Phase 1
    Basic Chat             :2024-01-03, 2d
    section Phase 2
    Agent Foundation       :2024-01-05, 3d
    section Phase 3
    Core Features         :2024-01-08, 5d
    section Phase 4
    RAG System            :2024-01-13, 4d
    section Phase 5
    Advanced Agents       :2024-01-17, 4d
    section Phase 6
    Study Mode            :2024-01-21, 3d
    section Phase 7
    Polish                :2024-01-24, 2d
```

## Common Pitfalls & Solutions

### Pitfall 1: Context Window Overflow
**Solution**: Use codebase flattener, but only include relevant modules for each iteration

### Pitfall 2: Placeholder Code
**Solution**: Explicit "NO PLACEHOLDERS" in every prompt + test validation

### Pitfall 3: Lost Implementation Details
**Solution**: Document every decision in code comments

### Pitfall 4: Dependency Conflicts
**Solution**: Lock versions in package.json from day 1

### Pitfall 5: State Management Chaos
**Solution**: Use consistent store pattern (Zustand recommended)

## Final Architecture Visualization

```
┌─────────────────────────────────────┐
│         React Frontend              │
│  ┌─────────────────────────────┐    │
│  │     Chat Interface         │    │
│  │  ┌──────┐ ┌──────────┐    │    │
│  │  │Status│ │ Messages  │    │    │
│  │  └──────┘ └──────────┘    │    │
│  └─────────────────────────────┘    │
│  ┌─────────────────────────────┐    │
│  │    Feature Components       │    │
│  │  ┌──────┐ ┌──────┐ ┌─────┐│    │
│  │  │ Todo │ │ Cal  │ │Notes││    │
│  │  └──────┘ └──────┘ └─────┘│    │
│  └─────────────────────────────┘    │
└─────────────────────────────────────┘
                 │
    ┌────────────▼────────────┐
    │   LangGraph Orchestrator│
    │  ┌──────────────────┐   │
    │  │  State Manager   │   │
    │  └──────────────────┘   │
    │  ┌──────────────────┐   │
    │  │  Agent Router    │   │
    │  └──────────────────┘   │
    └─────────────────────────┘
                 │
    ┌────────────▼────────────┐
    │      Tool System        │
    │  ┌─────┐ ┌─────┐ ┌────┐│
    │  │Todo │ │Cal  │ │RAG ││
    │  └─────┘ └─────┘ └────┘│
    └─────────────────────────┘
                 │
    ┌────────────▼────────────┐
    │     Data Stores         │
    │  ┌─────────────────┐    │
    │  │  Vector Store   │    │
    │  └─────────────────┘    │
    │  ┌─────────────────┐    │
    │  │  Local Storage  │    │
    │  └─────────────────┘    │
    └─────────────────────────┘
```

## Ready to Start?

1. **Set up your environment** with Node.js and required tools
2. **Start with Phase 0** - spend time on planning, it pays off
3. **Follow the iteration pattern** religiously
4. **Test everything** before moving forward
5. **Keep context updated** after each iteration

This system has been proven to work because it:
- Maintains context throughout development
- Tests prevent regression
- Incremental approach prevents overwhelming the AI
- Documentation-first prevents missing implementations
- Clear structure guides the AI coder effectively