<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lang<PERSON><PERSON>n <PERSON>G Chat</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .chat-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chat-history {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
        }
        .user-message {
            background-color: #e3f2fd;
            margin-left: 20%;
        }
        .ai-message {
            background-color: #f5f5f5;
            margin-right: 20%;
        }
        .input-area {
            display: flex;
            gap: 10px;
        }
        #user-input {
            flex-grow: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            background-color: #2196f3;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #1976d2;
        }
        button:disabled {
            background-color: #bbdefb;
            cursor: not-allowed;
        }
        .session-controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        #session-id {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <h1>LangChain RAG Chat</h1>
        
        <div class="session-controls">
            <label for="session-id">Session ID:</label>
            <input type="text" id="session-id" placeholder="Enter session ID" value="session1">
            <button onclick="clearHistory()">Clear History</button>
        </div>
        
        <div class="chat-history" id="chat-history"></div>
        
        <div class="input-area">
            <input type="text" id="user-input" placeholder="Type your message..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()" id="send-button">Send</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        // Load chat history on page load
        window.onload = function() {
            loadChatHistory();
        };
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        async function sendMessage() {
            const userInput = document.getElementById('user-input');
            const message = userInput.value.trim();
            const sessionId = document.getElementById('session-id').value;
            
            if (!message) return;
            
            const sendButton = document.getElementById('send-button');
            sendButton.disabled = true;
            userInput.disabled = true;
            
            // Add user message to chat
            addMessageToChat('user', message);
            userInput.value = '';
            
            try {
                // Send message to backend
                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        session_id: sessionId,
                        message: message
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Add AI response to chat
                addMessageToChat('ai', data.response);
            } catch (error) {
                console.error('Error:', error);
                addMessageToChat('ai', 'Sorry, I encountered an error. Please try again.');
            } finally {
                sendButton.disabled = false;
                userInput.disabled = false;
                userInput.focus();
            }
        }
        
        async function clearHistory() {
            const sessionId = document.getElementById('session-id').value;
            
            try {
                const response = await fetch(`${API_BASE}/history/${sessionId}`, {
                    method: 'DELETE'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                // Clear chat display
                document.getElementById('chat-history').innerHTML = '';
            } catch (error) {
                console.error('Error clearing history:', error);
                alert('Error clearing history');
            }
        }
        
        function addMessageToChat(role, content) {
            const chatHistory = document.getElementById('chat-history');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;
            messageDiv.textContent = content;
            chatHistory.appendChild(messageDiv);
            
            // Scroll to bottom
            chatHistory.scrollTop = chatHistory.scrollHeight;
        }
        
        async function loadChatHistory() {
            const sessionId = document.getElementById('session-id').value;
            
            try {
                const response = await fetch(`${API_BASE}/history/${sessionId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Clear current chat display
                const chatHistory = document.getElementById('chat-history');
                chatHistory.innerHTML = '';
                
                // Add history messages to chat
                data.history.forEach(msg => {
                    addMessageToChat(msg.role, msg.content);
                });
            } catch (error) {
                console.error('Error loading history:', error);
            }
        }
    </script>
</body>
</html>