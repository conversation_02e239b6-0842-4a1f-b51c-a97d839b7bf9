# Extending the LangChain RAG Application

This document provides guidance on how to extend and improve the basic LangChain RAG application we've built.

## 1. Adding More Document Sources

### Web Documents
To add more web documents to the RAG pipeline, modify the `create_rag_chain()` function in `rag_chain.py`:

```python
# Add more URLs to the loader
urls = [
    "https://en.wikipedia.org/wiki/Artificial_intelligence",
    "https://en.wikipedia.org/wiki/Machine_learning",
    "https://en.wikipedia.org/wiki/Deep_learning"
]
loader = WebBaseLoader(urls)
```

### PDF Documents
To process PDF files:

1. Install the required package:
   ```bash
   pip install pypdf
   ```

2. Modify `rag_chain.py`:
   ```python
   from langchain_community.document_loaders import PyPDFLoader
   
   # Load PDF documents
   loader = PyPDFLoader("path/to/your/document.pdf")
   documents = loader.load()
   ```

### Database Documents
To load documents from a database:

1. Install the required package:
   ```bash
   pip install sqlalchemy
   ```

2. Modify `rag_chain.py`:
   ```python
   from langchain_community.utilities import SQLDatabase
   from langchain_community.document_loaders import DatabaseLoader
   
   # Load from database
   db = SQLDatabase.from_uri("postgresql://user:password@localhost/dbname")
   loader = DatabaseLoader(db)
   documents = loader.load()
   ```

## 2. Improving the RAG Pipeline

### Better Text Splitting
Use semantic chunking for better context preservation:

```python
from langchain_experimental.text_splitter import SemanticChunker

text_splitter = SemanticChunker(embeddings)
splits = text_splitter.split_documents(documents)
```

### Advanced Retrieval
Implement Maximal Marginal Relevance (MMR) for diverse results:

```python
retriever = vectorstore.as_retriever(search_type="mmr", search_kwargs={"k": 5, "fetch_k": 20})
```

### Re-ranking
Add a re-ranking step to improve retrieval quality:

```python
from langchain_community.document_compressors import CohereRerank

compressor = CohereRerank()
compression_retriever = ContextualCompressionRetriever(
    base_compressor=compressor, base_retriever=retriever
)
```

## 3. Enhancing the Chat Interface

### Streaming Responses
Implement streaming for real-time responses:

```python
# In rag_chain.py
from langchain_core.callbacks import AsyncIteratorCallbackHandler

async def create_rag_chain_with_streaming():
    callback = AsyncIteratorCallbackHandler()
    
    llm = ChatOpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key=os.getenv("OPENROUTER_API_KEY"),
        model="z-ai/glm-4.5-air:free",
        callbacks=[callback],
        streaming=True
    )
    
    # ... rest of chain setup
    
    return rag_chain, callback
```

### Rich UI Components
Add support for markdown rendering, code blocks, and file uploads in the frontend.

## 4. Advanced Features

### Multi-Model Support
Allow users to select different models:

```python
# In rag_chain.py
SUPPORTED_MODELS = {
    "glm": "z-ai/glm-4.5-air:free",
    "llama": "meta-llama/llama-3.1-8b-instruct:free",
    "phi": "microsoft/phi-3-mini-128k-instruct:free"
}

def create_rag_chain(model_name="glm"):
    model = SUPPORTED_MODELS.get(model_name, SUPPORTED_MODELS["glm"])
    
    llm = ChatOpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key=os.getenv("OPENROUTER_API_KEY"),
        model=model,
        temperature=0.7
    )
    
    # ... rest of chain setup
```

### Agent Integration
Add agents for complex tasks:

```python
from langchain.agents import AgentExecutor, create_tool_calling_agent
from langchain.tools import tool

@tool
def calculator(expression: str) -> str:
    """Performs mathematical calculations"""
    try:
        result = eval(expression)
        return str(result)
    except:
        return "Error in calculation"

def create_agent_rag_chain():
    # ... existing RAG setup ...
    
    tools = [calculator]
    agent = create_tool_calling_agent(llm, tools, prompt)
    agent_executor = AgentExecutor(agent=agent, tools=tools)
    
    return agent_executor
```

### Evaluation and Monitoring
Add evaluation metrics:

```python
# In a new evaluation.py file
from langchain.evaluation import load_evaluator

def evaluate_response(question, response, reference):
    evaluator = load_evaluator("qa")
    result = evaluator.evaluate_strings(
        prediction=response,
        reference=reference,
        input=question
    )
    return result
```

## 5. Production Improvements

### Caching
Add Redis caching for embeddings and LLM responses:

```python
from langchain.cache import RedisCache
import redis

redis_client = redis.Redis(host='localhost', port=6379, db=0)
langchain.llm_cache = RedisCache(redis_client)
```

### Error Handling
Improve error handling in all components:

```python
# In chat_history.py
def add_to_history(self, session_id: str, user_message: str, ai_response: str):
    try:
        with self._get_connection() as conn:
            with conn.cursor() as cur:
                # Add user message
                cur.execute("""
                    INSERT INTO chat_history (session_id, message_type, content)
                    VALUES (%s, %s, %s)
                """, (session_id, 'user', user_message))
                
                # Add AI response
                cur.execute("""
                    INSERT INTO chat_history (session_id, message_type, content)
                    VALUES (%s, %s, %s)
                """, (session_id, 'ai', ai_response))
                
                conn.commit()
    except psycopg2.OperationalError as e:
        # Handle database connection issues
        print(f"Database connection error: {e}")
        # Implement retry logic or fallback
    except Exception as e:
        print(f"Error adding to history: {e}")
        # Log error and possibly notify monitoring system
```

### Monitoring
Add LangSmith for tracing:

```python
# In main.py
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_ENDPOINT"] = "https://api.smith.langchain.com"
os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGSMITH_API_KEY")
os.environ["LANGCHAIN_PROJECT"] = "RAG-App"
```

## 6. Security Enhancements

### Input Validation
Add input sanitization:

```python
import re

def sanitize_input(text):
    # Remove potentially harmful characters
    sanitized = re.sub(r'[<>&]', '', text)
    # Limit length
    return sanitized[:1000]
```

### Rate Limiting
Implement rate limiting in FastAPI:

```python
from fastapi import FastAPI, Request
from collections import defaultdict
import time

app = FastAPI()
request_counts = defaultdict(list)

@app.middleware("http")
async def rate_limit_middleware(request: Request, call_next):
    client_ip = request.client.host
    current_time = time.time()
    
    # Remove requests older than 1 minute
    request_counts[client_ip] = [
        req_time for req_time in request_counts[client_ip] 
        if current_time - req_time < 60
    ]
    
    # Check if limit exceeded (e.g., 10 requests per minute)
    if len(request_counts[client_ip]) >= 10:
        return JSONResponse(
            status_code=429,
            content={"detail": "Rate limit exceeded"}
        )
    
    request_counts[client_ip].append(current_time)
    response = await call_next(request)
    return response
```

## 7. Performance Optimizations

### Asynchronous Processing
Use async/await for better performance:

```python
# In rag_chain.py
async def create_rag_chain():
    # ... existing setup ...
    
    # Use async methods where available
    async def async_format_docs(docs):
        return "\n\n".join(doc.page_content for doc in docs)
    
    # ... rest of async chain setup
```

### Connection Pooling
Implement connection pooling for database access:

```python
# In chat_history.py
from psycopg2 import pool

class ChatHistoryManager:
    def __init__(self):
        self.connection_pool = pool.SimpleConnectionPool(
            1, 20,  # min and max connections
            **self.db_params
        )
    
    def _get_connection(self):
        return self.connection_pool.getconn()
    
    def _return_connection(self, conn):
        self.connection_pool.putconn(conn)
```

These extensions can significantly enhance the functionality, performance, and robustness of your LangChain RAG application.